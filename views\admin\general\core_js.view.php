<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/adm_assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/adm_assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/adm_assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert function ?>
<script>
    function showSweetAlertSuccess(title, message) {
        swal({
            title : title,
            text  : message,
            icon  : 'success',
            button: {
                text: "Ok", // Texto del botón
                value: true, // Valor devuelto en la promesa .then()
                visible: true,
                className: "btn-success", // <-- Clase CSS (Bootstrap para verde)
                closeModal: true
            }
        });
    }
    
    function showSweetAlertError(title, message) {
        swal({
            title : title,
            text  : message,
            icon  : 'error',
            button: {
                text: "Cerrar",
                value: null, // O false si quieres diferenciarlo en .then()
                visible: true,
                className: "btn-danger", // <-- Clase CSS (Bootstrap para rojo)
                closeModal: true
            }
        });
    }
</script>
<?php #endregion JS sweet alert function ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
	<script type="text/javascript">
        showSweetAlertSuccess("<?php echo $success_text; ?>");
	</script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
	<script type="text/javascript">
        showSweetAlertError("<?php echo $error_text; ?>");
	</script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>